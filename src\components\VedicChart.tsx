'use client';

import React from 'react';
import { VedicChart as Vedic<PERSON>hartD<PERSON>, ChartHouse } from '@/lib/astrology';
import { useUITranslation } from '@/utils/ui-translations';

interface VedicChartProps {
  chartData: VedicChartData;
  title: string;
  className?: string;
}

export default function VedicChart({ chartData, title, className = '' }: VedicChartProps) {
  const { t } = useUITranslation();

  // Function to translate zodiac signs
  const translateSign = (sign: string): string => {
    const signMap: { [key: string]: string } = {
      'Aries': t('aries'),
      'Taurus': t('taurus'),
      'Gemini': t('gemini'),
      'Cancer': t('cancer'),
      'Leo': t('leo'),
      'Virgo': t('virgo'),
      'Libra': t('libra'),
      'Scorpio': t('scorpio'),
      'Sagittarius': t('sagittarius'),
      'Capricorn': t('capricorn'),
      'Aquarius': t('aquarius'),
      'Pisces': t('pisces')
    };
    return signMap[sign] || sign;
  };

  // Function to translate planet names
  const translatePlanet = (planet: string): string => {
    const planetMap: { [key: string]: string } = {
      'Sun': t('sun'),
      'Moon': t('moon'),
      'Mars': t('mars'),
      'Mercury': t('mercury'),
      'Jupiter': t('jupiter'),
      'Venus': t('venus'),
      'Saturn': t('saturn'),
      'Rahu': t('rahu'),
      'Ketu': t('ketu'),
      'Uranus': t('uranus'),
      'Neptune': t('neptune'),
      'Pluto': t('pluto')
    };
    return planetMap[planet] || planet;
  };

  // Diamond chart layout - traditional Vedic style with enhanced visuals
  const renderDiamondChart = () => {
    const houses = chartData.houses;

    return (
      <div className="relative w-full max-w-96 h-80 sm:h-96 mx-auto">
        <svg viewBox="0 0 400 400" className="w-full h-full drop-shadow-lg">
          {/* Enhanced background with multiple gradients and effects */}
          <defs>
            <radialGradient id="chartRadialGradient" cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor="#4c1d95" stopOpacity="0.9"/>
              <stop offset="30%" stopColor="#312e81" stopOpacity="0.7"/>
              <stop offset="70%" stopColor="#1e1b4b" stopOpacity="0.8"/>
              <stop offset="100%" stopColor="#0f0f23" stopOpacity="0.9"/>
            </radialGradient>
            <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#f97316"/>
              <stop offset="25%" stopColor="#fbbf24"/>
              <stop offset="50%" stopColor="#f97316"/>
              <stop offset="75%" stopColor="#ea580c"/>
              <stop offset="100%" stopColor="#f97316"/>
            </linearGradient>
            <filter id="enhancedGlow">
              <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
            <filter id="innerShadow">
              <feOffset dx="0" dy="0"/>
              <feGaussianBlur stdDeviation="2" result="offset-blur"/>
              <feFlood floodColor="#000000" floodOpacity="0.3"/>
              <feComposite in2="offset-blur" operator="in"/>
            </filter>
          </defs>

          {/* Chart background - diamond shape with enhanced styling */}
          <polygon
            points="200,20 380,200 200,380 20,200"
            fill="url(#chartRadialGradient)"
            stroke="url(#borderGradient)"
            strokeWidth="4"
            filter="url(#enhancedGlow)"
            style={{ filter: 'drop-shadow(0 0 10px rgba(249, 115, 22, 0.3))' }}
          />

          {/* Internal lines to create houses with enhanced styling and gradients */}
          {/* Horizontal lines */}
          <line x1="20" y1="200" x2="380" y2="200" stroke="url(#borderGradient)" strokeWidth="2.5" opacity="0.9" />
          <line x1="110" y1="110" x2="290" y2="290" stroke="url(#borderGradient)" strokeWidth="2.5" opacity="0.9" />
          <line x1="110" y1="290" x2="290" y2="110" stroke="url(#borderGradient)" strokeWidth="2.5" opacity="0.9" />

          {/* Vertical lines */}
          <line x1="200" y1="20" x2="200" y2="380" stroke="url(#borderGradient)" strokeWidth="2.5" opacity="0.9" />

          {/* House numbers and content */}
          {renderHouseContent(houses)}
        </svg>
      </div>
    );
  };

  const renderHouseContent = (houses: ChartHouse[]) => {
    // House positions in the diamond chart - perfectly centered and spaced
    const housePositions = [
      { x: 200, y: 75, house: 1 },   // Top
      { x: 280, y: 120, house: 2 },  // Top right
      { x: 325, y: 165, house: 3 },  // Right top
      { x: 325, y: 235, house: 4 },  // Right bottom
      { x: 280, y: 280, house: 5 },  // Bottom right
      { x: 200, y: 325, house: 6 },  // Bottom
      { x: 120, y: 280, house: 7 },  // Bottom left
      { x: 75, y: 235, house: 8 },   // Left bottom
      { x: 75, y: 165, house: 9 },   // Left top
      { x: 120, y: 120, house: 10 }, // Top left
      { x: 165, y: 75, house: 11 },  // Top left corner
      { x: 235, y: 75, house: 12 }   // Top right corner
    ];

    return housePositions.map(pos => {
      const house = houses.find(h => h.houseNumber === pos.house);
      if (!house) return null;

      return (
        <g key={pos.house}>
          {/* House number with enhanced styling and perfect centering */}
          <text
            x={pos.x}
            y={pos.y - 25}
            textAnchor="middle"
            dominantBaseline="middle"
            className="text-sm font-bold fill-orange-200"
            style={{
              textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
              filter: 'drop-shadow(0 0 2px rgba(249, 115, 22, 0.5))'
            }}
          >
            {pos.house}
          </text>

          {/* Sign name (translated) with perfect centering */}
          <text
            x={pos.x}
            y={pos.y - 8}
            textAnchor="middle"
            dominantBaseline="middle"
            className="text-xs fill-blue-100 font-semibold"
            style={{
              textShadow: '1px 1px 3px rgba(0,0,0,0.8)',
              filter: 'drop-shadow(0 0 1px rgba(59, 130, 246, 0.5))'
            }}
          >
            {translateSign(house.sign)}
          </text>

          {/* Planets in this house with perfect positioning and centering */}
          {house.planets.map((planet, index) => {
            // Calculate planet positions for perfect centering
            const numPlanets = house.planets.length;
            let planetX, planetY;

            if (numPlanets === 1) {
              planetX = pos.x;
              planetY = pos.y + 15;
            } else if (numPlanets === 2) {
              planetX = pos.x + (index === 0 ? -12 : 12);
              planetY = pos.y + 15;
            } else {
              planetX = pos.x + (index % 2 === 0 ? -12 : 12);
              planetY = pos.y + 12 + Math.floor(index / 2) * 12;
            }

            return (
              <g key={`${pos.house}-${planet.name}-${index}`}>
                {/* Planet symbol with enhanced styling and perfect centering */}
                <text
                  x={planetX}
                  y={planetY}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  className={`text-xs font-bold ${getPlanetColor(planet.name)}`}
                  style={{
                    textShadow: '2px 2px 4px rgba(0,0,0,0.9)',
                    filter: 'drop-shadow(0 0 2px rgba(255,255,255,0.3))'
                  }}
                >
                  {planet.symbol}
                  {planet.retrograde && (
                    <tspan className="text-red-200 text-xs font-bold">ᴿ</tspan>
                  )}
                </text>
              </g>
            );
          })}
        </g>
      );
    });
  };

  const getPlanetColor = (planetName: string): string => {
    const colors: { [key: string]: string } = {
      'Sun': 'fill-yellow-300',
      'Moon': 'fill-blue-200',
      'Mars': 'fill-red-300',
      'Mercury': 'fill-green-300',
      'Jupiter': 'fill-yellow-200',
      'Venus': 'fill-pink-300',
      'Saturn': 'fill-purple-300',
      'Rahu': 'fill-gray-300',
      'Ketu': 'fill-gray-200',
      'Uranus': 'fill-cyan-300',
      'Neptune': 'fill-blue-300',
      'Pluto': 'fill-indigo-300'
    };
    return colors[planetName] || 'fill-white';
  };

  return (
    <div className={`relative bg-gradient-to-br from-purple-900/40 to-blue-900/40 backdrop-blur-md rounded-3xl p-8 md:p-10 border border-purple-400/30 shadow-2xl overflow-hidden ${className}`}>
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-500/5 rounded-3xl"></div>
      <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.1),transparent_50%)]"></div>
      <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_70%_80%,rgba(59,130,246,0.1),transparent_50%)]"></div>

      <div className="relative z-10">
        <h3 className="text-2xl md:text-3xl font-bold text-center text-white mb-8 md:mb-10 bg-gradient-to-r from-purple-200 via-blue-200 to-purple-200 bg-clip-text text-transparent drop-shadow-lg">
          ✨ {title} ✨
        </h3>
        {renderDiamondChart()}

        {/* Enhanced Legend with beautiful styling */}
        <div className="mt-8 md:mt-10 space-y-4">
          <div className="bg-gradient-to-r from-black/30 to-black/20 backdrop-blur-md rounded-xl p-6 border border-white/20 shadow-lg">
            <div className="text-sm md:text-base text-gray-100 text-center space-y-3">
              <p className="font-semibold text-purple-100 flex items-center justify-center">
                <span className="mr-2">🏠</span>
                {t('houses_numbered_clockwise')}
              </p>
              <p className="hidden md:block text-blue-100 flex items-center justify-center">
                <span className="mr-2">🌟</span>
                {t('planet_abbreviations')}
              </p>
              <p className="text-orange-100 flex items-center justify-center">
                <span className="mr-2">🔄</span>
                {t('retrograde_motion')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
