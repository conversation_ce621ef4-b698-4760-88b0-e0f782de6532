'use client';

import React from 'react';
import { Vedic<PERSON>hart as VedicChartData, ChartHouse } from '@/lib/astrology';
import { useUITranslation } from '@/utils/ui-translations';

interface VedicChartProps {
  chartData: VedicChartData;
  title: string;
  className?: string;
}

export default function VedicChart({ chartData, title, className = '' }: VedicChartProps) {
  const { t } = useUITranslation();

  // Diamond chart layout - traditional Vedic style
  const renderDiamondChart = () => {
    const houses = chartData.houses;
    
    return (
      <div className="relative w-full max-w-80 h-64 sm:h-80 mx-auto">
        <svg viewBox="0 0 400 400" className="w-full h-full">
          {/* Chart border - diamond shape */}
          <polygon
            points="200,20 380,200 200,380 20,200"
            fill="none"
            stroke="#f97316"
            strokeWidth="2"
          />
          
          {/* Internal lines to create houses */}
          {/* Horizontal lines */}
          <line x1="20" y1="200" x2="380" y2="200" stroke="#f97316" strokeWidth="1" />
          <line x1="110" y1="110" x2="290" y2="290" stroke="#f97316" strokeWidth="1" />
          <line x1="110" y1="290" x2="290" y2="110" stroke="#f97316" strokeWidth="1" />
          
          {/* Vertical lines */}
          <line x1="200" y1="20" x2="200" y2="380" stroke="#f97316" strokeWidth="1" />
          
          {/* House numbers and content */}
          {renderHouseContent(houses)}
        </svg>
      </div>
    );
  };

  const renderHouseContent = (houses: ChartHouse[]) => {
    // House positions in the diamond chart
    const housePositions = [
      { x: 200, y: 65, house: 1 },   // Top
      { x: 290, y: 110, house: 2 },  // Top right
      { x: 335, y: 155, house: 3 },  // Right top
      { x: 335, y: 245, house: 4 },  // Right bottom
      { x: 290, y: 290, house: 5 },  // Bottom right
      { x: 200, y: 335, house: 6 },  // Bottom
      { x: 110, y: 290, house: 7 },  // Bottom left
      { x: 65, y: 245, house: 8 },   // Left bottom
      { x: 65, y: 155, house: 9 },   // Left top
      { x: 110, y: 110, house: 10 }, // Top left
      { x: 155, y: 65, house: 11 },  // Top left corner
      { x: 245, y: 65, house: 12 }   // Top right corner
    ];

    return housePositions.map(pos => {
      const house = houses.find(h => h.houseNumber === pos.house);
      if (!house) return null;

      return (
        <g key={pos.house}>
          {/* House number */}
          <text
            x={pos.x}
            y={pos.y - 15}
            textAnchor="middle"
            className="text-xs font-bold fill-orange-400"
          >
            {pos.house}
          </text>
          
          {/* Sign abbreviation */}
          <text
            x={pos.x}
            y={pos.y}
            textAnchor="middle"
            className="text-xs fill-blue-300"
          >
            {house.signShort}
          </text>
          
          {/* Planets in this house */}
          {house.planets.map((planet, index) => (
            <text
              key={`${pos.house}-${planet.name}-${index}`}
              x={pos.x + (index % 2 === 0 ? -10 : 10)}
              y={pos.y + 15 + Math.floor(index / 2) * 12}
              textAnchor="middle"
              className={`text-xs font-semibold ${getPlanetColor(planet.name)}`}
            >
              {planet.symbol}
              {planet.retrograde && (
                <tspan className="text-red-400">ᴿ</tspan>
              )}
            </text>
          ))}
        </g>
      );
    });
  };

  const getPlanetColor = (planetName: string): string => {
    const colors: { [key: string]: string } = {
      'Sun': 'fill-yellow-400',
      'Moon': 'fill-blue-200',
      'Mars': 'fill-red-400',
      'Mercury': 'fill-green-400',
      'Jupiter': 'fill-yellow-300',
      'Venus': 'fill-pink-400',
      'Saturn': 'fill-purple-400',
      'Rahu': 'fill-gray-400',
      'Ketu': 'fill-gray-300',
      'Uranus': 'fill-cyan-400',
      'Neptune': 'fill-blue-400',
      'Pluto': 'fill-indigo-400'
    };
    return colors[planetName] || 'fill-white';
  };

  return (
    <div className={`bg-gradient-to-br from-purple-900/20 to-blue-900/20 rounded-xl p-4 md:p-6 ${className}`}>
      <h3 className="text-lg md:text-xl font-bold text-center text-white mb-3 md:mb-4">{title}</h3>
      {renderDiamondChart()}

      {/* Legend */}
      <div className="mt-3 md:mt-4 text-xs text-gray-300 text-center space-y-1">
        <p className="hidden sm:block">{t('houses_numbered_clockwise')}</p>
        <p className="hidden md:block">{t('planet_abbreviations')}</p>
        <p className="hidden sm:block">{t('retrograde_motion')}</p>
      </div>
    </div>
  );
}
