'use client';

import { useState, useEffect } from 'react';
import { User } from '@/types';
import { Search, Star, User as UserIcon, Sparkles, RefreshCw, Calendar, MapPin, Clock, Eye, Plus } from 'lucide-react';
import { useConfirmDialog, useAlertDialog } from '@/contexts/DialogContext';

interface BirthChart {
  id: string;
  userId: string;
  birthDateTime: string;
  birthPlace: string;
  birthLatitude: number;
  birthLongitude: number;
  timezone: string;
  ascendant: string;
  moonSign: string;
  sunSign: string;
  generalReading: string;
  strengthsWeaknesses: string;
  careerGuidance: string;
  relationshipGuidance: string;
  healthGuidance: string;
  spiritualGuidance: string;
  readingsEn?: any;
  readingsSi?: any;
  calculatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    zodiacSign: string;
    birthDate: string;
    birthTime?: string;
    birthPlace?: string;
  };
}

export default function UnifiedHoroscopeManagement() {
  const [birthCharts, setBirthCharts] = useState<BirthChart[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedChart, setSelectedChart] = useState<BirthChart | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [regenerating, setRegenerating] = useState<string | null>(null);
  const { confirmDelete } = useConfirmDialog();
  const { showSuccess, showError } = useAlertDialog();

  useEffect(() => {
    fetchBirthCharts();
    fetchUsers();
  }, []);

  const fetchBirthCharts = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/birth-charts', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      if (data.success) {
        setBirthCharts(data.data);
      }
    } catch (error) {
      console.error('Error fetching birth charts:', error);
      showError('Error', 'Failed to fetch birth charts');
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/users', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      if (data.success) {
        setUsers(data.data.users);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const handleGenerateBirthChart = async (userId: string, userName: string) => {
    const confirmed = await confirmDelete(
      'Generate Birth Chart',
      `Generate birth chart for ${userName}? This will calculate their personalized horoscope based on birth details.`
    );

    if (!confirmed) return;

    try {
      setRegenerating(userId);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/birth-charts/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ userId })
      });

      const data = await response.json();

      if (data.success) {
        showSuccess('Success', 'Birth chart generated successfully!');
        fetchBirthCharts();
      } else {
        showError('Generation Failed', data.error || 'Unknown error occurred');
      }
    } catch (error) {
      console.error('Error generating birth chart:', error);
      showError('Error', 'Error generating birth chart. Please try again.');
    } finally {
      setRegenerating(null);
    }
  };

  const handleRegenerateBirthChart = async (userId: string, userName: string) => {
    const confirmed = await confirmDelete(
      'Regenerate Birth Chart',
      `Regenerate birth chart for ${userName}? This will recalculate their horoscope with updated interpretations.`
    );

    if (!confirmed) return;

    try {
      setRegenerating(userId);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/birth-charts/regenerate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ userId })
      });

      const data = await response.json();

      if (data.success) {
        showSuccess('Success', 'Birth chart regenerated successfully!');
        fetchBirthCharts();
      } else {
        showError('Regeneration Failed', data.error || 'Unknown error occurred');
      }
    } catch (error) {
      console.error('Error regenerating birth chart:', error);
      showError('Error', 'Error regenerating birth chart. Please try again.');
    } finally {
      setRegenerating(null);
    }
  };

  const handleViewDetails = (chart: BirthChart) => {
    setSelectedChart(chart);
    setShowDetailsModal(true);
  };

  // Filter birth charts based on search term
  const filteredCharts = birthCharts.filter(chart =>
    chart.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chart.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chart.user.zodiacSign.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get users without birth charts
  const usersWithoutCharts = users.filter(user => 
    user.birthDate && user.birthTime && user.birthLatitude && user.birthLongitude &&
    !birthCharts.some(chart => chart.userId === user.id)
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-400"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center">
            <Sparkles className="mr-2 text-purple-400" size={24} />
            User Birth Charts (Handahana)
          </h2>
          <p className="text-gray-300 mt-1">
            View and manage automatically generated birth charts that users see in their dashboard
          </p>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
        <input
          type="text"
          placeholder="Search by name, email, or zodiac sign..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
        />
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-yellow-900/20 to-orange-900/20 backdrop-blur-sm rounded-xl p-6 border border-yellow-500/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-yellow-300 text-sm font-medium">Generated Charts</p>
              <p className="text-3xl font-bold text-white mt-1">{birthCharts.length}</p>
            </div>
            <div className="bg-yellow-500/20 rounded-full p-3">
              <Star className="w-8 h-8 text-yellow-400" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-green-900/20 to-emerald-900/20 backdrop-blur-sm rounded-xl p-6 border border-green-500/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-300 text-sm font-medium">Pending Generation</p>
              <p className="text-3xl font-bold text-white mt-1">{usersWithoutCharts.length}</p>
            </div>
            <div className="bg-green-500/20 rounded-full p-3">
              <Plus className="w-8 h-8 text-green-400" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 backdrop-blur-sm rounded-xl p-6 border border-blue-500/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-300 text-sm font-medium">Total Users</p>
              <p className="text-3xl font-bold text-white mt-1">{users.length}</p>
            </div>
            <div className="bg-blue-500/20 rounded-full p-3">
              <UserIcon className="w-8 h-8 text-blue-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Users Without Charts */}
      {usersWithoutCharts.length > 0 && (
        <div className="space-y-6">
          <h3 className="text-xl font-bold text-white flex items-center">
            <div className="bg-green-500/20 rounded-full p-2 mr-3">
              <Plus className="text-green-400" size={20} />
            </div>
            Users Ready for Birth Chart Generation ({usersWithoutCharts.length})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {usersWithoutCharts.map((user) => (
              <div key={user.id} className="bg-gradient-to-br from-green-900/20 to-emerald-900/20 backdrop-blur-sm rounded-xl p-6 border border-green-500/20 hover:border-green-400/30 transition-all duration-200">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="bg-blue-500/20 rounded-full p-2">
                    <UserIcon className="text-blue-400" size={20} />
                  </div>
                  <div>
                    <h4 className="text-white font-bold text-lg">{user.name}</h4>
                    <p className="text-green-300 text-sm font-medium">{user.zodiacSign}</p>
                  </div>
                </div>
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-300">
                    <Calendar size={14} className="mr-2 text-green-400" />
                    {new Date(user.birthDate).toLocaleDateString()}
                  </div>
                  <div className="flex items-center text-sm text-gray-300">
                    <Clock size={14} className="mr-2 text-green-400" />
                    {user.birthTime}
                  </div>
                  <div className="flex items-center text-sm text-gray-300">
                    <MapPin size={14} className="mr-2 text-green-400" />
                    {user.birthPlace}
                  </div>
                </div>
                <button
                  onClick={() => handleGenerateBirthChart(user.id, user.name)}
                  disabled={regenerating === user.id}
                  className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:from-gray-600 disabled:to-gray-700 text-white px-4 py-3 rounded-lg font-medium transition-all duration-200 flex items-center justify-center shadow-lg"
                >
                  {regenerating === user.id ? (
                    <RefreshCw className="animate-spin" size={18} />
                  ) : (
                    <>
                      <Plus size={18} className="mr-2" />
                      Generate Chart
                    </>
                  )}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Birth Charts List */}
      <div className="space-y-6">
        <h3 className="text-xl font-bold text-white flex items-center">
          <div className="bg-yellow-500/20 rounded-full p-2 mr-3">
            <Star className="text-yellow-400" size={20} />
          </div>
          Generated Birth Charts ({filteredCharts.length})
        </h3>

        {filteredCharts.length > 0 ? (
          <div className="grid grid-cols-1 gap-6">
            {filteredCharts.map((chart) => (
              <div key={chart.id} className="bg-gradient-to-br from-purple-900/40 to-blue-900/40 backdrop-blur-sm rounded-2xl p-8 border border-purple-500/20 shadow-2xl hover:border-purple-400/30 transition-all duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 mb-6">
                      <div className="bg-blue-500/20 rounded-full p-3">
                        <UserIcon className="text-blue-400" size={24} />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-white">{chart.user.name}</h3>
                        <span className="px-3 py-1 rounded-full text-sm bg-purple-500/20 text-purple-300 font-medium">
                          {chart.user.zodiacSign}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                      <div className="bg-purple-800/20 rounded-xl p-4 border border-purple-500/20">
                        <p className="text-purple-300 text-sm font-medium mb-3">Birth Details</p>
                        <div className="space-y-2">
                          <p className="text-white flex items-center">
                            <Calendar className="mr-2 text-purple-400" size={16} />
                            {new Date(chart.birthDateTime).toLocaleDateString()}
                          </p>
                          <p className="text-white flex items-center">
                            <Clock className="mr-2 text-purple-400" size={16} />
                            {new Date(chart.birthDateTime).toLocaleTimeString()}
                          </p>
                          <p className="text-white flex items-center">
                            <MapPin className="mr-2 text-purple-400" size={16} />
                            {chart.birthPlace}
                          </p>
                        </div>
                      </div>

                      <div className="bg-purple-800/20 rounded-xl p-4 border border-purple-500/20">
                        <p className="text-purple-300 text-sm font-medium mb-3">Astrological Signs</p>
                        <div className="space-y-2">
                          <p className="text-white flex items-center">
                            <span className="text-yellow-400 mr-2">☀️</span>
                            Sun: {chart.sunSign}
                          </p>
                          <p className="text-white flex items-center">
                            <span className="text-blue-400 mr-2">🌙</span>
                            Moon: {chart.moonSign}
                          </p>
                          <p className="text-white flex items-center">
                            <span className="text-green-400 mr-2">⬆️</span>
                            Ascendant: {chart.ascendant}
                          </p>
                        </div>
                      </div>

                      <div className="bg-purple-800/20 rounded-xl p-4 border border-purple-500/20">
                        <p className="text-purple-300 text-sm font-medium mb-3">Chart Status</p>
                        <div className="space-y-2">
                          <p className="text-green-400 flex items-center">
                            <span className="mr-2">✅</span>
                            Generated
                          </p>
                          <p className="text-gray-300 text-sm">
                            {new Date(chart.calculatedAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                </div>
                
                <div className="flex space-x-3">
                  <button
                    onClick={() => handleViewDetails(chart)}
                    className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center shadow-lg"
                  >
                    <Eye size={18} className="mr-2" />
                    View Details
                  </button>
                  <button
                    onClick={() => handleRegenerateBirthChart(chart.userId, chart.user.name)}
                    disabled={regenerating === chart.userId}
                    className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:from-gray-600 disabled:to-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center shadow-lg"
                  >
                    {regenerating === chart.userId ? (
                      <RefreshCw className="animate-spin" size={18} />
                    ) : (
                      <>
                        <RefreshCw size={18} className="mr-2" />
                        Regenerate
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-16">
            <div className="bg-gradient-to-br from-gray-800/20 to-gray-900/20 rounded-2xl p-8 border border-gray-500/20 max-w-md mx-auto">
              <Sparkles className="mx-auto mb-4 text-gray-400" size={64} />
              <h3 className="text-xl font-semibold text-white mb-2">No Birth Charts Found</h3>
              <p className="text-gray-300">
                {searchTerm ? 'No birth charts match your search criteria.' : 'Birth charts are automatically generated when users complete their profile with birth details.'}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Details Modal */}
      {showDetailsModal && selectedChart && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold text-white flex items-center">
                  <Sparkles className="mr-2 text-purple-400" size={24} />
                  {selectedChart.user.name}'s Birth Chart
                </h3>
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  ✕
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Birth Information */}
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                  <h4 className="text-lg font-bold text-white mb-3 flex items-center">
                    <Calendar className="mr-2 text-blue-400" size={18} />
                    Birth Information
                  </h4>
                  <div className="space-y-2 text-sm">
                    <p className="text-gray-300">
                      <span className="text-white font-semibold">Date:</span> {new Date(selectedChart.birthDateTime).toLocaleDateString()}
                    </p>
                    <p className="text-gray-300">
                      <span className="text-white font-semibold">Time:</span> {new Date(selectedChart.birthDateTime).toLocaleTimeString()}
                    </p>
                    <p className="text-gray-300">
                      <span className="text-white font-semibold">Place:</span> {selectedChart.birthPlace}
                    </p>
                    <p className="text-gray-300">
                      <span className="text-white font-semibold">Timezone:</span> {selectedChart.timezone}
                    </p>
                  </div>
                </div>

                {/* Astrological Signs */}
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                  <h4 className="text-lg font-bold text-white mb-3 flex items-center">
                    <Star className="mr-2 text-yellow-400" size={18} />
                    Astrological Signs
                  </h4>
                  <div className="space-y-2 text-sm">
                    <p className="text-gray-300">
                      <span className="text-white font-semibold">☀️ Sun Sign:</span> {selectedChart.sunSign}
                    </p>
                    <p className="text-gray-300">
                      <span className="text-white font-semibold">🌙 Moon Sign:</span> {selectedChart.moonSign}
                    </p>
                    <p className="text-gray-300">
                      <span className="text-white font-semibold">⬆️ Ascendant:</span> {selectedChart.ascendant}
                    </p>
                  </div>
                </div>
              </div>

              {/* Readings */}
              <div className="mt-6 space-y-4">
                <h4 className="text-lg font-bold text-white">Birth Chart Readings (What User Sees)</h4>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div className="bg-white/5 rounded-lg p-4">
                    <h5 className="text-white font-semibold mb-2">General Reading</h5>
                    <p className="text-gray-300 text-sm leading-relaxed">{selectedChart.generalReading}</p>
                  </div>

                  <div className="bg-white/5 rounded-lg p-4">
                    <h5 className="text-white font-semibold mb-2">Strengths & Weaknesses</h5>
                    <p className="text-gray-300 text-sm leading-relaxed">{selectedChart.strengthsWeaknesses}</p>
                  </div>

                  <div className="bg-white/5 rounded-lg p-4">
                    <h5 className="text-white font-semibold mb-2">Career Guidance</h5>
                    <p className="text-gray-300 text-sm leading-relaxed">{selectedChart.careerGuidance}</p>
                  </div>

                  <div className="bg-white/5 rounded-lg p-4">
                    <h5 className="text-white font-semibold mb-2">Relationship Guidance</h5>
                    <p className="text-gray-300 text-sm leading-relaxed">{selectedChart.relationshipGuidance}</p>
                  </div>

                  <div className="bg-white/5 rounded-lg p-4">
                    <h5 className="text-white font-semibold mb-2">Health Guidance</h5>
                    <p className="text-gray-300 text-sm leading-relaxed">{selectedChart.healthGuidance}</p>
                  </div>

                  <div className="bg-white/5 rounded-lg p-4">
                    <h5 className="text-white font-semibold mb-2">Spiritual Guidance</h5>
                    <p className="text-gray-300 text-sm leading-relaxed">{selectedChart.spiritualGuidance}</p>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    handleRegenerateBirthChart(selectedChart.userId, selectedChart.user.name);
                    setShowDetailsModal(false);
                  }}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center"
                >
                  <RefreshCw size={16} className="mr-1" />
                  Regenerate Chart
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
