'use client';

import React from 'react';
import {
  KarakData,
  AvasthaData,
  PlanetaryDetail,
  VimshottariDashaData,
  AshtakavargaData
} from '@/lib/astrology';
import { useUITranslation } from '@/utils/ui-translations';

interface AstrologicalTablesProps {
  karakTable?: KarakData;
  avasthaTable?: AvasthaData;
  planetaryDetails?: PlanetaryDetail[];
  vimshottariDasha?: VimshottariDashaData;
  ashtakavarga?: AshtakavargaData;
}

export default function AstrologicalTables({
  karakTable,
  avasthaTable,
  planetaryDetails,
  vimshottariDasha,
  ashtakavarga
}: AstrologicalTablesProps) {
  const { t } = useUITranslation();

  // Function to translate planet names
  const translatePlanet = (planet: string): string => {
    const planetMap: { [key: string]: string } = {
      'Sun': t('sun'),
      'Moon': t('moon'),
      'Mars': t('mars'),
      'Mercury': t('mercury'),
      'Jupiter': t('jupiter'),
      'Venus': t('venus'),
      'Saturn': t('saturn'),
      'Rahu': t('rahu'),
      'Ketu': t('ketu'),
      'Uranus': t('uranus'),
      'Neptune': t('neptune'),
      'Pluto': t('pluto')
    };
    return planetMap[planet] || planet;
  };

  // Function to translate zodiac signs
  const translateSign = (sign: string): string => {
    const signMap: { [key: string]: string } = {
      'Aries': t('aries'),
      'Taurus': t('taurus'),
      'Gemini': t('gemini'),
      'Cancer': t('cancer'),
      'Leo': t('leo'),
      'Virgo': t('virgo'),
      'Libra': t('libra'),
      'Scorpio': t('scorpio'),
      'Sagittarius': t('sagittarius'),
      'Capricorn': t('capricorn'),
      'Aquarius': t('aquarius'),
      'Pisces': t('pisces')
    };
    return signMap[sign] || sign;
  };

  const renderKarakTable = () => {
    if (!karakTable) return null;
    
    return (
      <div className="bg-gradient-to-br from-yellow-900/30 to-orange-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-yellow-500/20 shadow-2xl">
        <h3 className="text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent text-center">
          {t('karaka')}
        </h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm md:text-base">
            <thead>
              <tr className="border-b-2 border-orange-500/40">
                <th className="text-left py-3 text-orange-200 font-bold">Karak</th>
                <th className="text-left py-3 text-orange-200 font-bold">Sthir</th>
                <th className="text-left py-3 text-orange-200 font-bold">Chara</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(karakTable).map(([karak, data]) => (
                <tr key={karak} className="border-b border-orange-500/20 hover:bg-orange-500/10 transition-colors">
                  <td className="py-3 text-white font-medium">{karak}</td>
                  <td className="py-3 text-yellow-100 font-medium">{translatePlanet(data.sthir)}</td>
                  <td className="py-3 text-yellow-100 font-medium">{translatePlanet(data.chara)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderAvasthaTable = () => {
    if (!avasthaTable) return null;

    return (
      <div className="bg-gradient-to-br from-blue-900/30 to-purple-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-blue-500/20 shadow-2xl">
        <h3 className="text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-blue-300 to-purple-300 bg-clip-text text-transparent text-center">
          {t('avastha')}
        </h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm md:text-base">
            <thead>
              <tr className="border-b-2 border-blue-500/40">
                <th className="text-left py-3 text-blue-200 font-bold">{t('planets')}</th>
                <th className="text-left py-3 text-blue-200 font-bold">{t('jagrat')}</th>
                <th className="text-left py-3 text-blue-200 font-bold">{t('baladi')}</th>
                <th className="text-left py-3 text-blue-200 font-bold">{t('deeptadi')}</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(avasthaTable).map(([planet, states]) => (
                <tr key={planet} className="border-b border-blue-500/20 hover:bg-blue-500/10 transition-colors">
                  <td className="py-3 text-white font-medium">{translatePlanet(planet)}</td>
                  <td className="py-3 text-blue-100 font-medium">{states.jagrat}</td>
                  <td className="py-3 text-blue-100 font-medium">{states.baladi}</td>
                  <td className="py-3 text-blue-100 font-medium">{states.deeptadi}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderPlanetaryDetails = () => {
    if (!planetaryDetails) return null;
    
    return (
      <div className="bg-gradient-to-br from-green-900/30 to-teal-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-green-500/20 shadow-2xl">
        <h3 className="text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-green-300 to-teal-300 bg-clip-text text-transparent text-center">
          {t('planetary_details')}
        </h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm md:text-base">
            <thead>
              <tr className="border-b-2 border-green-500/40">
                <th className="text-left py-3 text-green-200 font-bold">{t('planets')}</th>
                <th className="text-center py-3 text-green-200 font-bold">{t('c_r')}</th>
                <th className="text-left py-3 text-green-200 font-bold">{t('rashi')}</th>
                <th className="text-left py-3 text-green-200 font-bold">{t('longitude')}</th>
                <th className="text-left py-3 text-green-200 font-bold">{t('nakshatra')}</th>
                <th className="text-center py-3 text-green-200 font-bold">{t('pada')}</th>
                <th className="text-left py-3 text-green-200 font-bold">{t('relation')}</th>
              </tr>
            </thead>
            <tbody>
              {planetaryDetails.map((detail) => (
                <tr key={detail.planet} className="border-b border-green-500/20 hover:bg-green-500/10 transition-colors">
                  <td className="py-3 text-white font-medium">{translatePlanet(detail.planet)}</td>
                  <td className="py-3 text-center">
                    <span className={detail.combust ? 'text-red-300 font-bold' : 'text-gray-400'}>
                      {detail.combust ? 'C' : '-'}
                    </span>
                    <span className="ml-2">
                      <span className={detail.retrograde ? 'text-red-300 font-bold' : 'text-gray-400'}>
                        {detail.retrograde ? 'R' : '-'}
                      </span>
                    </span>
                  </td>
                  <td className="py-3 text-green-100 font-medium">{translateSign(detail.rashi)}</td>
                  <td className="py-3 text-green-100 font-medium">{detail.longitude}</td>
                  <td className="py-3 text-green-100 font-medium">{detail.nakshatra}</td>
                  <td className="py-3 text-center text-green-100 font-medium">{detail.pada}</td>
                  <td className="py-3">
                    <span className={getRelationColor(detail.relation)}>
                      {detail.relation}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderVimshottariDasha = () => {
    if (!vimshottariDasha) return null;

    return (
      <div className="bg-gradient-to-br from-purple-900/30 to-pink-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-purple-500/20 shadow-2xl">
        <h3 className="text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent text-center">
          {t('vimshottari_dasha')}
        </h3>

        <div className="mb-6 p-6 bg-purple-800/30 backdrop-blur-sm rounded-xl border border-purple-500/20">
          <p className="text-purple-100 text-lg">
            <span className="font-bold text-purple-200">{t('balance_of_dasha')}:</span> {vimshottariDasha.balance}
          </p>
          <p className="text-purple-100 text-lg mt-2">
            <span className="font-bold text-purple-200">{t('current_dasha')}:</span> {translatePlanet(vimshottariDasha.currentDasha)}
          </p>
        </div>

        <div className="grid grid-cols-1 gap-3">
          {vimshottariDasha.periods.slice(0, 9).map((period, index) => (
            <div key={index} className="flex justify-between items-center p-4 bg-purple-800/20 backdrop-blur-sm rounded-lg border border-purple-500/10 hover:bg-purple-500/20 transition-colors">
              <span className="text-white font-bold text-lg">{translatePlanet(period.planet)}</span>
              <span className="text-purple-100 font-medium">
                {new Date(period.startDate).toLocaleDateString()} - {new Date(period.endDate).toLocaleDateString()}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderAshtakavarga = () => {
    if (!ashtakavarga) return null;
    
    return (
      <div className="bg-gradient-to-br from-indigo-900/20 to-blue-900/20 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Prastharashtakvarga</h3>
        
        {/* Sun Table */}
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-yellow-300 mb-2">SUN</h4>
          <div className="overflow-x-auto">
            <table className="w-full text-xs border border-yellow-500/30">
              <thead>
                <tr className="bg-yellow-900/20">
                  <th className="border border-yellow-500/30 p-1 text-yellow-300"></th>
                  {['Ar', 'Ta', 'Ge', 'Ca', 'Le', 'Vi', 'Li', 'Sc', 'Sa', 'Ca', 'Aq', 'Pi', 'Total'].map(sign => (
                    <th key={sign} className="border border-yellow-500/30 p-1 text-yellow-300">{sign}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {['SUN', 'MOON', 'MARS', 'MERCURY', 'JUPITER', 'VENUS', 'SATURN', 'Ascendant'].map((planet, planetIndex) => (
                  <tr key={planet}>
                    <td className="border border-yellow-500/30 p-1 text-white font-medium">{planet}</td>
                    {ashtakavarga.sunTable[planetIndex]?.map((score, houseIndex) => (
                      <td key={houseIndex} className="border border-yellow-500/30 p-1 text-center text-yellow-200">
                        {score}
                      </td>
                    )) || Array(12).fill(0).map((_, i) => (
                      <td key={i} className="border border-yellow-500/30 p-1 text-center text-yellow-200">0</td>
                    ))}
                    <td className="border border-yellow-500/30 p-1 text-center text-yellow-300 font-bold">
                      {ashtakavarga.sunTable[planetIndex]?.reduce((sum, score) => sum + score, 0) || 0}
                    </td>
                  </tr>
                ))}
                <tr className="bg-yellow-900/20">
                  <td className="border border-yellow-500/30 p-1 text-yellow-300 font-bold">Total</td>
                  {Array(12).fill(0).map((_, houseIndex) => {
                    const total = ashtakavarga.sunTable.reduce((sum, row) => sum + (row[houseIndex] || 0), 0);
                    return (
                      <td key={houseIndex} className="border border-yellow-500/30 p-1 text-center text-yellow-300 font-bold">
                        {total}
                      </td>
                    );
                  })}
                  <td className="border border-yellow-500/30 p-1 text-center text-yellow-300 font-bold">
                    {ashtakavarga.sarvashtakavarga.reduce((sum, score) => sum + score, 0)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Sarvashtakavarga Summary */}
        <div className="mt-4 p-4 bg-indigo-800/20 rounded-lg">
          <h4 className="text-lg font-semibold text-indigo-300 mb-2">Sarvashtakavarga Summary</h4>
          <div className="grid grid-cols-6 gap-2 text-sm">
            {ashtakavarga.sarvashtakavarga.map((score, index) => (
              <div key={index} className="text-center">
                <div className="text-indigo-300">House {index + 1}</div>
                <div className="text-white font-bold">{score}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const getRelationColor = (relation: string): string => {
    switch (relation) {
      case 'Own': return 'text-green-400';
      case 'Friendly': return 'text-blue-400';
      case 'Enemy': return 'text-red-400';
      case 'Neutral': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {renderKarakTable()}
        {renderAvasthaTable()}
      </div>
      
      {renderPlanetaryDetails()}
      {renderVimshottariDasha()}
      {renderAshtakavarga()}
    </div>
  );
}
